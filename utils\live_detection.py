"""
Live video detection using webcam or video stream for real-time object detection.
"""

import cv2
import numpy as np
import time
import threading
from typing import List, Tuple, Optional, Dict, Any, Callable
from queue import Queue
import streamlit as st
from PIL import Image

try:
    from .object_extraction import ObjectExtractor
    from ..models.clip_model import CLIPMatcher
except ImportError:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.object_extraction import ObjectExtractor
    from models.clip_model import CLIPMatcher


class LiveVideoDetector:
    """Real-time video detection using webcam or video stream."""
    
    def __init__(self, clip_model_name: str = "openai/clip-vit-base-patch32"):
        """
        Initialize live video detector.
        
        Args:
            clip_model_name: CLIP model to use for matching
        """
        self.clip_model_name = clip_model_name
        self.clip_matcher = None
        self.object_extractor = None
        
        # Video capture
        self.cap = None
        self.is_running = False
        self.current_frame = None
        self.detection_results = Queue(maxsize=10)
        
        # Detection settings
        self.query = ""
        self.similarity_threshold = 0.25
        self.detection_interval = 0.5  # Detect every 0.5 seconds
        self.last_detection_time = 0
        
        # Threading
        self.capture_thread = None
        self.detection_thread = None
        self.frame_lock = threading.Lock()
        
        print("Live video detector initialized")
    
    def initialize_models(self):
        """Initialize AI models (lazy loading)."""
        if self.clip_matcher is None:
            print("Loading CLIP model for live detection...")
            self.clip_matcher = CLIPMatcher(model_name=self.clip_model_name)
        
        if self.object_extractor is None:
            print("Loading object extractor for live detection...")
            try:
                self.object_extractor = ObjectExtractor()
            except Exception as e:
                print(f"Warning: Object extractor not available: {e}")
                self.object_extractor = None
    
    def start_camera(self, camera_index: int = 0) -> bool:
        """
        Start camera capture.
        
        Args:
            camera_index: Camera index (0 for default camera)
            
        Returns:
            True if camera started successfully
        """
        try:
            self.cap = cv2.VideoCapture(camera_index)
            
            if not self.cap.isOpened():
                print(f"Error: Could not open camera {camera_index}")
                return False
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            print(f"Camera {camera_index} started successfully")
            return True
            
        except Exception as e:
            print(f"Error starting camera: {e}")
            return False
    
    def start_video_file(self, video_path: str) -> bool:
        """
        Start video file playback.

        Args:
            video_path: Path to video file

        Returns:
            True if video started successfully
        """
        try:
            self.cap = cv2.VideoCapture(video_path)

            if not self.cap.isOpened():
                print(f"Error: Could not open video file {video_path}")
                return False

            print(f"Video file {video_path} started successfully")
            return True

        except Exception as e:
            print(f"Error starting video file: {e}")
            return False

    def start_stream(self, stream_url: str) -> bool:
        """
        Start live stream (RTSP, HTTP, YouTube, etc.).

        Args:
            stream_url: Stream URL (RTSP, HTTP, YouTube, etc.)

        Returns:
            True if stream started successfully
        """
        try:
            print(f"Connecting to stream: {stream_url}")

            # Handle different stream types
            if "youtube.com" in stream_url or "youtu.be" in stream_url:
                # YouTube stream handling
                try:
                    import yt_dlp
                    print("🔍 Resolving YouTube stream URL...")

                    ydl_opts = {
                        'format': 'best[height<=720]/best[height<=480]/best',
                        'quiet': True,
                        'no_warnings': True,
                        'extract_flat': False,
                        'ignoreerrors': True,
                    }

                    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                        info = ydl.extract_info(stream_url, download=False)

                        if info and 'url' in info:
                            original_url = stream_url
                            stream_url = info['url']
                            print(f"✅ YouTube stream resolved successfully")
                            print(f"   Title: {info.get('title', 'Unknown')}")
                            print(f"   Duration: {info.get('duration', 'Live/Unknown')} seconds")
                        else:
                            print("⚠️ Could not extract stream URL from YouTube")
                            return False

                except ImportError:
                    print("❌ yt-dlp not installed. Install with: pip install yt-dlp")
                    print("YouTube streams require yt-dlp for URL resolution")
                    return False
                except Exception as e:
                    print(f"❌ Could not resolve YouTube URL: {e}")
                    print("This might be a private video, live stream that ended, or network issue")
                    return False

            # Create video capture with stream URL
            self.cap = cv2.VideoCapture(stream_url)

            # Set buffer size to reduce latency
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

            # Set timeout for network streams
            if stream_url.startswith(('rtsp://', 'http://', 'https://')):
                self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 10000)  # 10 seconds
                self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)   # 5 seconds

            if not self.cap.isOpened():
                print(f"Error: Could not connect to stream {stream_url}")
                return False

            # Test reading a frame
            print("🔄 Testing stream connection...")
            ret, frame = self.cap.read()
            if not ret:
                print(f"❌ Could not read from stream")
                print("💡 Troubleshooting tips:")
                print("   - Check if the stream URL is correct")
                print("   - Verify the stream is currently active")
                print("   - For YouTube: ensure it's a live stream or public video")
                print("   - For RTSP: check camera is online and accessible")
                self.cap.release()
                return False

            print(f"✅ Stream connected successfully!")
            print(f"   Resolution: {frame.shape[1]}x{frame.shape[0]}")
            print(f"   Stream URL: {stream_url[:50]}{'...' if len(stream_url) > 50 else ''}")
            return True

        except Exception as e:
            print(f"Error starting stream: {e}")
            return False
    
    def start_detection(self, query: str, similarity_threshold: float = 0.25,
                       detection_interval: float = 0.5):
        """
        Start live detection.
        
        Args:
            query: Search query
            similarity_threshold: Minimum similarity for detection
            detection_interval: Time between detections in seconds
        """
        if self.cap is None:
            print("Error: No video source available")
            return False
        
        self.query = query
        self.similarity_threshold = similarity_threshold
        self.detection_interval = detection_interval
        self.is_running = True
        
        # Initialize models
        self.initialize_models()
        
        # Start threads
        self.capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
        self.detection_thread = threading.Thread(target=self._detection_loop, daemon=True)
        
        self.capture_thread.start()
        self.detection_thread.start()
        
        print(f"Live detection started for query: '{query}'")
        return True
    
    def stop_detection(self):
        """Stop live detection."""
        self.is_running = False
        
        if self.capture_thread:
            self.capture_thread.join(timeout=1.0)
        
        if self.detection_thread:
            self.detection_thread.join(timeout=1.0)
        
        if self.cap:
            self.cap.release()
            self.cap = None
        
        print("Live detection stopped")
    
    def _capture_loop(self):
        """Main capture loop running in separate thread."""
        while self.is_running and self.cap and self.cap.isOpened():
            ret, frame = self.cap.read()
            
            if not ret:
                print("Error reading frame or end of video")
                break
            
            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            with self.frame_lock:
                self.current_frame = frame_rgb.copy()
            
            time.sleep(0.033)  # ~30 FPS
    
    def _detection_loop(self):
        """Detection loop running in separate thread."""
        while self.is_running:
            current_time = time.time()
            
            if current_time - self.last_detection_time >= self.detection_interval:
                with self.frame_lock:
                    if self.current_frame is not None:
                        frame_to_process = self.current_frame.copy()
                    else:
                        time.sleep(0.1)
                        continue
                
                # Perform detection
                try:
                    results = self._detect_in_frame(frame_to_process)
                    
                    # Add to results queue
                    if not self.detection_results.full():
                        self.detection_results.put({
                            'timestamp': current_time,
                            'frame': frame_to_process,
                            'results': results,
                            'query': self.query
                        })
                    
                    self.last_detection_time = current_time
                    
                except Exception as e:
                    print(f"Error in detection: {e}")
            
            time.sleep(0.1)
    
    def _detect_in_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        Detect objects in a single frame.
        
        Args:
            frame: Input frame (RGB)
            
        Returns:
            Detection results
        """
        results = {
            'matches': [],
            'objects': [],
            'similarity_score': 0.0,
            'detected': False
        }
        
        try:
            # CLIP-based detection using direct similarity calculation
            if self.clip_matcher:
                # Use direct CLIP similarity calculation (more reliable for live detection)
                from PIL import Image

                # Convert frame to PIL Image
                pil_image = Image.fromarray(frame)

                # Get CLIP similarity directly
                similarity = self.clip_matcher.calculate_similarity(pil_image, self.query)

                results['similarity_score'] = float(similarity)
                results['detected'] = similarity >= self.similarity_threshold

                if results['detected']:
                    print(f"🎯 Detection! Score: {results['similarity_score']:.3f} for '{self.query}'")
                    # Create a match tuple for compatibility
                    results['matches'] = [(0, frame, 0.0, similarity)]
                else:
                    # Still log the score for debugging
                    if similarity > 0.1:  # Only log if there's some similarity
                        print(f"🔍 Checking... Score: {results['similarity_score']:.3f} (threshold: {self.similarity_threshold:.3f})")
            
            # Object extraction
            if results['detected'] and self.object_extractor:
                try:
                    extracted_objects = self.object_extractor.extract_objects_from_frame(
                        frame, self.query, confidence_threshold=0.3, max_objects=5
                    )
                    results['objects'] = extracted_objects
                    if extracted_objects:
                        print(f"🎯 Extracted {len(extracted_objects)} objects")
                except Exception as obj_error:
                    print(f"⚠️ Object extraction error: {obj_error}")
                    results['objects'] = []

        except Exception as e:
            print(f"❌ Error in frame detection: {e}")
            import traceback
            traceback.print_exc()

        return results
    
    def get_current_frame(self) -> Optional[np.ndarray]:
        """Get the current frame."""
        with self.frame_lock:
            return self.current_frame.copy() if self.current_frame is not None else None
    
    def get_latest_detection(self) -> Optional[Dict[str, Any]]:
        """Get the latest detection result."""
        if not self.detection_results.empty():
            return self.detection_results.get()
        return None
    
    def get_detection_stats(self) -> Dict[str, Any]:
        """Get detection statistics."""
        return {
            'is_running': self.is_running,
            'query': self.query,
            'similarity_threshold': self.similarity_threshold,
            'detection_interval': self.detection_interval,
            'queue_size': self.detection_results.qsize(),
            'has_camera': self.cap is not None and self.cap.isOpened()
        }


class StreamlitLiveDetection:
    """Streamlit integration for live video detection."""
    
    def __init__(self):
        """Initialize Streamlit live detection."""
        if 'live_detector' not in st.session_state:
            st.session_state.live_detector = None
        
        if 'live_detection_active' not in st.session_state:
            st.session_state.live_detection_active = False
        
        if 'detection_results' not in st.session_state:
            st.session_state.detection_results = []
    
    def render_live_detection_interface(self):
        """Render the live detection interface."""
        st.header("📹 Live Video Detection")
        
        # Video source selection
        video_source = st.selectbox(
            "📹 Video Source",
            options=["Webcam", "Live Stream", "Video File"],
            help="Choose your video input source"
        )

        # Source-specific configuration
        if video_source == "Webcam":
            col1, col2 = st.columns(2)
            with col1:
                camera_index = st.number_input(
                    "Camera Index",
                    min_value=0,
                    max_value=10,
                    value=0,
                    help="Camera index (0 for default camera)"
                )
            with col2:
                st.info("💡 Use 0 for built-in camera, 1+ for external cameras")

        elif video_source == "Live Stream":
            # Check if a test stream was selected
            default_url = ""
            if hasattr(st.session_state, 'test_stream'):
                default_url = st.session_state.test_stream
                del st.session_state.test_stream

            stream_url = st.text_input(
                "🌐 Stream URL",
                value=default_url,
                placeholder="rtsp://*************:554/stream or https://youtube.com/watch?v=...",
                help="Enter RTSP, HTTP, or YouTube live stream URL"
            )

            # Stream type examples
            with st.expander("📡 Stream URL Examples & Working Streams"):
                st.markdown("""
                **RTSP Streams:**
                - `rtsp://*************:554/stream`
                - `rtsp://username:password@*************:554/stream`

                **HTTP/HTTPS Streams:**
                - `http://*************:8080/video`
                - `https://example.com/live.m3u8`

                **YouTube Examples (Working Streams):**
                - Wildlife: `https://www.youtube.com/watch?v=ydYDqZQpim8` (African Wildlife)
                - Traffic: `https://www.youtube.com/watch?v=1EiC9bvVGnk` (Times Square)
                - Nature: `https://www.youtube.com/watch?v=RtycCFcNADg` (Bird Feeder)
                - City: `https://www.youtube.com/watch?v=adLGHcj_fmA` (City Traffic)

                **IP Cameras:**
                - Most IP cameras support RTSP
                - Check camera documentation for stream URL

                **💡 Tips:**
                - For YouTube: Use live streams or long videos
                - Test stream in VLC player first
                - RTSP streams are most reliable for continuous monitoring
                """)

                # Quick test buttons for working streams
                st.markdown("**🚀 Quick Test Streams:**")
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("🦁 Wildlife Stream"):
                        st.session_state.test_stream = "https://www.youtube.com/watch?v=ydYDqZQpim8"
                with col2:
                    if st.button("🚗 Traffic Stream"):
                        st.session_state.test_stream = "https://www.youtube.com/watch?v=1EiC9bvVGnk"

        else:  # Video File
            uploaded_file = st.file_uploader(
                "📁 Choose video file",
                type=['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm'],
                help="Upload a video file for live detection"
            )
        
        # Detection settings
        st.subheader("🎯 Detection Settings")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            query = st.text_input(
                "Search Query",
                value="person",
                help="What to look for in the live video"
            )
        
        with col2:
            similarity_threshold = st.slider(
                "Detection Threshold",
                min_value=0.1,
                max_value=0.5,
                value=0.25,
                step=0.05,
                help="Higher values = more accurate detection"
            )
        
        with col3:
            detection_interval = st.slider(
                "Detection Interval (seconds)",
                min_value=0.1,
                max_value=2.0,
                value=0.5,
                step=0.1,
                help="Time between detections"
            )
        
        # Control buttons
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🚀 Start Detection", type="primary"):
                if video_source == "Webcam":
                    source_param = camera_index
                elif video_source == "Live Stream":
                    source_param = stream_url if 'stream_url' in locals() else ""
                else:  # Video File
                    source_param = uploaded_file

                self._start_detection(video_source, query, similarity_threshold,
                                    detection_interval, source_param)
        
        with col2:
            if st.button("⏹️ Stop Detection"):
                self._stop_detection()
        
        with col3:
            if st.button("🔄 Refresh"):
                st.rerun()
        
        # Display detection status and results
        self._display_detection_status()
        self._display_live_results()
    
    def _start_detection(self, video_source: str, query: str, threshold: float, 
                        interval: float, source_param):
        """Start live detection."""
        try:
            # Initialize detector
            if st.session_state.live_detector is None:
                st.session_state.live_detector = LiveVideoDetector()
            
            detector = st.session_state.live_detector
            
            # Stop any existing detection
            if st.session_state.live_detection_active:
                detector.stop_detection()
            
            # Start video source
            success = False
            if video_source == "Webcam":
                success = detector.start_camera(camera_index=source_param)
            elif video_source == "Live Stream":
                if source_param and source_param.strip():
                    success = detector.start_stream(source_param.strip())
                else:
                    st.error("Please enter a valid stream URL")
                    return
            else:  # Video File
                if source_param is not None:
                    # Save uploaded file temporarily
                    import tempfile
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as tmp_file:
                        tmp_file.write(source_param.read())
                        temp_path = tmp_file.name

                    success = detector.start_video_file(temp_path)
                else:
                    st.error("Please upload a video file")
                    return
            
            if success:
                # Start detection
                detector.start_detection(query, threshold, interval)
                st.session_state.live_detection_active = True
                st.session_state.detection_results = []
                st.success(f"✅ Live detection started for '{query}'")
            else:
                st.error("❌ Failed to start video source")
                
        except Exception as e:
            st.error(f"Error starting detection: {e}")
    
    def _stop_detection(self):
        """Stop live detection."""
        try:
            if st.session_state.live_detector and st.session_state.live_detection_active:
                st.session_state.live_detector.stop_detection()
                st.session_state.live_detection_active = False
                st.success("⏹️ Detection stopped")
            else:
                st.warning("No active detection to stop")
                
        except Exception as e:
            st.error(f"Error stopping detection: {e}")
    
    def _display_detection_status(self):
        """Display current detection status."""
        if st.session_state.live_detector and st.session_state.live_detection_active:
            stats = st.session_state.live_detector.get_detection_stats()
            
            st.subheader("📊 Detection Status")
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Status", "🟢 Active" if stats['is_running'] else "🔴 Inactive")
            
            with col2:
                st.metric("Query", stats['query'])
            
            with col3:
                st.metric("Threshold", f"{stats['similarity_threshold']:.2f}")
            
            with col4:
                st.metric("Queue Size", stats['queue_size'])
    
    def _display_live_results(self):
        """Display live detection results."""
        if not st.session_state.live_detection_active:
            return
        
        detector = st.session_state.live_detector
        if not detector:
            return
        
        # Get current frame
        current_frame = detector.get_current_frame()
        if current_frame is not None:
            st.subheader("📹 Live Video Feed")
            
            # Display current frame
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.image(current_frame, caption="Live Video", use_container_width=True)
            
            with col2:
                # Get latest detection
                latest_detection = detector.get_latest_detection()
                
                if latest_detection and latest_detection['results']['detected']:
                    st.success("✅ Object Detected!")
                    st.metric("Similarity Score", f"{latest_detection['results']['similarity_score']:.3f}")
                    
                    # Display extracted objects
                    if latest_detection['results']['objects']:
                        st.write("🎯 Extracted Objects:")
                        for i, (obj_img, confidence, obj_class) in enumerate(latest_detection['results']['objects']):
                            st.image(obj_img, caption=f"{obj_class} ({confidence:.2f})", width=150)
                else:
                    st.info("🔍 Searching...")
        
        # Display recent detections
        self._display_recent_detections()
    
    def _display_recent_detections(self):
        """Display recent detection results."""
        if not st.session_state.live_detector:
            return
        
        # Collect recent detections
        recent_detections = []
        while True:
            detection = st.session_state.live_detector.get_latest_detection()
            if detection is None:
                break
            
            if detection['results']['detected']:
                recent_detections.append(detection)
                st.session_state.detection_results.append(detection)
        
        # Keep only last 10 detections
        st.session_state.detection_results = st.session_state.detection_results[-10:]
        
        if st.session_state.detection_results:
            st.subheader("🎯 Recent Detections")
            
            # Display in grid
            cols = st.columns(3)
            for i, detection in enumerate(reversed(st.session_state.detection_results[-6:])):
                with cols[i % 3]:
                    timestamp = time.strftime("%H:%M:%S", time.localtime(detection['timestamp']))
                    score = detection['results']['similarity_score']
                    
                    st.image(detection['frame'], caption=f"{timestamp} | Score: {score:.3f}", width=200)
                    
                    # Show extracted objects
                    if detection['results']['objects']:
                        for obj_img, confidence, obj_class in detection['results']['objects'][:2]:
                            st.image(obj_img, caption=f"{obj_class}", width=100)


def create_live_detector() -> LiveVideoDetector:
    """Factory function to create a live video detector."""
    return LiveVideoDetector()
