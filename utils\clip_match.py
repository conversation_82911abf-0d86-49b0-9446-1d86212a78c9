"""
Core search engine that combines frame extraction, CLIP matching, and result processing.
"""

import os
import time
from typing import List, Tuple, Optional, Dict, Any
import numpy as np

try:
    # Try relative imports first (when used as package)
    from .frame_extraction import FrameExtractor
    from ..models.clip_model import C<PERSON><PERSON>Matcher
    from .video_slicing import VideoClipper
except ImportError:
    # Fall back to absolute imports (when run directly)
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.frame_extraction import FrameExtractor
    from models.clip_model import CLIPMatcher
    from utils.video_slicing import VideoClipper


class VideoSearchEngine:
    """Main search engine for AI-powered video content search."""
    
    def __init__(self,
                 clip_model_name: str = "openai/clip-vit-base-patch32",
                 output_dir: str = "static/output_clips",
                 frame_interval: int = 30,
                 max_frames: Optional[int] = None,
                 target_resolution: Optional[Tuple[int, int]] = (512, 384),
                 quality_factor: float = 0.8,
                 use_chunked_processing: bool = True,
                 chunk_size: int = 200):
        """
        Initialize the video search engine.

        Args:
            clip_model_name: CLIP model to use for matching
            output_dir: Directory for output clips and thumbnails
            frame_interval: Extract every N frames
            max_frames: Maximum frames to extract (None for no limit)
            target_resolution: Resize frames to this resolution for memory efficiency
            quality_factor: Frame compression quality (0.1-1.0)
            use_chunked_processing: Use chunked processing for large videos
            chunk_size: Number of frames to process at once
        """
        self.clip_model_name = clip_model_name
        self.output_dir = output_dir
        self.use_chunked_processing = use_chunked_processing
        self.chunk_size = chunk_size

        # Initialize components
        self.frame_extractor = FrameExtractor(
            every_n_frames=frame_interval,
            max_frames=max_frames,
            target_resolution=target_resolution,
            quality_factor=quality_factor
        )
        self.clip_matcher = None  # Lazy loading
        self.video_clipper = VideoClipper(output_dir=output_dir)

        # Cache for extracted frames
        self._frame_cache = {}
        self._video_info_cache = {}

        # Memory management
        self._max_cache_size_mb = 2048  # 2GB cache limit
    
    def _ensure_clip_matcher(self):
        """Lazy load the CLIP matcher to avoid loading model unnecessarily."""
        if self.clip_matcher is None:
            print("Loading CLIP model...")
            self.clip_matcher = CLIPMatcher(model_name=self.clip_model_name)
    
    def search_video(self, 
                    video_path: str, 
                    query: str,
                    similarity_threshold: float = 0.2,
                    top_k: Optional[int] = 20,
                    create_clips: bool = True,
                    clip_duration: float = 3.0,
                    create_thumbnails: bool = True) -> Dict[str, Any]:
        """
        Search for content in a video based on text query.
        
        Args:
            video_path: Path to the video file
            query: Text query to search for
            similarity_threshold: Minimum similarity score
            top_k: Maximum number of results to return
            create_clips: Whether to create video clips
            clip_duration: Duration of each clip in seconds
            create_thumbnails: Whether to create thumbnail images
            
        Returns:
            Dictionary containing search results and metadata
        """
        start_time = time.time()
        
        print(f"Starting video search for: '{query}'")
        print(f"Video: {video_path}")
        
        # Validate video file
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        # Get video info
        video_info = self._get_video_info(video_path)
        
        # Extract frames (with caching)
        frames = self._get_frames(video_path)
        
        if not frames:
            return {
                'query': query,
                'video_path': video_path,
                'video_info': video_info,
                'matches': [],
                'clips': [],
                'thumbnails': [],
                'processing_time': time.time() - start_time,
                'error': 'No frames extracted from video'
            }
        
        # Ensure CLIP matcher is loaded
        self._ensure_clip_matcher()
        
        # Search for matching frames
        matches = self.clip_matcher.search_frames(
            frames=frames,
            query=query,
            threshold=similarity_threshold,
            top_k=top_k
        )
        
        # Process results
        clips = []
        thumbnails = []
        
        if matches:
            if create_clips:
                print("Creating video clips...")
                clips = self.video_clipper.create_clips_from_matches(
                    video_path=video_path,
                    matches=matches,
                    clip_duration=clip_duration,
                    query_name=query
                )
            
            if create_thumbnails:
                print("Creating thumbnails...")
                thumbnails = self.video_clipper.extract_frame_thumbnails(matches)
        
        processing_time = time.time() - start_time
        
        # Prepare results
        results = {
            'query': query,
            'video_path': video_path,
            'video_info': video_info,
            'matches': [
                {
                    'frame_index': frame_idx,
                    'timestamp': timestamp,
                    'similarity_score': score,
                    'time_formatted': self._format_time(timestamp)
                }
                for frame_idx, _, timestamp, score in matches
            ],
            'clips': clips,
            'thumbnails': [{'path': path, 'score': score} for path, score in thumbnails],
            'processing_time': processing_time,
            'stats': {
                'total_frames_extracted': len(frames),
                'matches_found': len(matches),
                'clips_created': len(clips),
                'thumbnails_created': len(thumbnails),
                'similarity_threshold': similarity_threshold
            }
        }
        
        print(f"Search completed in {processing_time:.2f} seconds")
        print(f"Found {len(matches)} matches")
        
        return results
    
    def batch_search(self, 
                    video_path: str, 
                    queries: List[str],
                    similarity_threshold: float = 0.2,
                    create_clips: bool = True) -> Dict[str, Dict[str, Any]]:
        """
        Search for multiple queries in the same video.
        
        Args:
            video_path: Path to the video file
            queries: List of text queries
            similarity_threshold: Minimum similarity score
            create_clips: Whether to create video clips
            
        Returns:
            Dictionary mapping queries to their results
        """
        start_time = time.time()
        
        print(f"Starting batch search for {len(queries)} queries")
        
        # Get video info and frames once
        video_info = self._get_video_info(video_path)
        frames = self._get_frames(video_path)
        
        if not frames:
            return {}
        
        # Ensure CLIP matcher is loaded
        self._ensure_clip_matcher()
        
        # Batch search
        batch_matches = self.clip_matcher.batch_search(
            frames=frames,
            queries=queries,
            threshold=similarity_threshold
        )
        
        # Process results for each query
        results = {}
        for query, matches in batch_matches.items():
            clips = []
            if matches and create_clips:
                clips = self.video_clipper.create_clips_from_matches(
                    video_path=video_path,
                    matches=matches,
                    query_name=query
                )
            
            results[query] = {
                'query': query,
                'video_path': video_path,
                'video_info': video_info,
                'matches': [
                    {
                        'frame_index': frame_idx,
                        'timestamp': timestamp,
                        'similarity_score': score,
                        'time_formatted': self._format_time(timestamp)
                    }
                    for frame_idx, _, timestamp, score in matches
                ],
                'clips': clips,
                'stats': {
                    'matches_found': len(matches),
                    'clips_created': len(clips)
                }
            }
        
        processing_time = time.time() - start_time
        print(f"Batch search completed in {processing_time:.2f} seconds")
        
        return results
    
    def _get_frames(self, video_path: str) -> List[Tuple[int, np.ndarray, float]]:
        """Get frames from video with caching and memory management."""
        if video_path not in self._frame_cache:
            print("Extracting frames from video...")

            # Check video size and decide processing method
            video_info = self._get_video_info(video_path)
            video_duration = video_info['duration_seconds']
            estimated_frames = int(video_duration * video_info['fps'] / self.frame_extractor.every_n_frames)

            # Use chunked processing for large videos
            if self.use_chunked_processing and (video_duration > 300 or estimated_frames > 1000):  # 5+ minutes or 1000+ frames
                print(f"Large video detected ({video_duration:.1f}s, ~{estimated_frames} frames). Using chunked processing...")
                frames = self.frame_extractor.extract_frames_chunked(video_path, chunk_size=self.chunk_size)
            else:
                frames = self.frame_extractor.extract_frames(video_path)

            # Check memory usage and manage cache
            self._manage_cache_memory()
            self._frame_cache[video_path] = frames

        return self._frame_cache[video_path]
    
    def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """Get video info with caching."""
        if video_path not in self._video_info_cache:
            self._video_info_cache[video_path] = self.frame_extractor.get_video_info(video_path)
        return self._video_info_cache[video_path]
    
    def _format_time(self, seconds: float) -> str:
        """Format time in seconds to MM:SS format."""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def clear_cache(self):
        """Clear the frame cache to free memory."""
        self._frame_cache.clear()
        self._video_info_cache.clear()
        print("Cache cleared")
    
    def cleanup_outputs(self):
        """Clean up output directory."""
        self.video_clipper.cleanup_output_dir()
        print("Output directory cleaned")
    
    def _manage_cache_memory(self):
        """Manage cache memory usage to prevent out-of-memory errors."""
        total_memory_mb = self._estimate_cache_memory_usage()

        if total_memory_mb > self._max_cache_size_mb:
            print(f"Cache memory usage ({total_memory_mb:.1f} MB) exceeds limit ({self._max_cache_size_mb} MB)")
            print("Clearing oldest cached videos...")

            # Clear cache starting with oldest entries
            cache_items = list(self._frame_cache.items())
            for video_path, frames in cache_items:
                del self._frame_cache[video_path]
                if video_path in self._video_info_cache:
                    del self._video_info_cache[video_path]

                total_memory_mb = self._estimate_cache_memory_usage()
                if total_memory_mb <= self._max_cache_size_mb * 0.7:  # Keep 30% buffer
                    break

            print(f"Cache cleared. Current memory usage: {total_memory_mb:.1f} MB")

    def _estimate_cache_memory_usage(self) -> float:
        """Estimate memory usage of cached frames in MB."""
        total_bytes = 0
        for frames in self._frame_cache.values():
            for _, frame_array, _ in frames:
                total_bytes += frame_array.nbytes
        return total_bytes / (1024 * 1024)

    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about cached data."""
        memory_usage_mb = self._estimate_cache_memory_usage()
        return {
            'cached_videos': list(self._frame_cache.keys()),
            'total_cached_frames': sum(len(frames) for frames in self._frame_cache.values()),
            'memory_usage_mb': memory_usage_mb,
            'memory_limit_mb': self._max_cache_size_mb,
            'clip_matcher_loaded': self.clip_matcher is not None
        }


def create_search_engine(clip_model_name: str = "openai/clip-vit-base-patch32",
                        output_dir: str = "static/output_clips",
                        frame_interval: int = 30) -> VideoSearchEngine:
    """
    Factory function to create a video search engine.
    
    Args:
        clip_model_name: CLIP model to use
        output_dir: Output directory for clips
        frame_interval: Frame extraction interval
        
    Returns:
        VideoSearchEngine instance
    """
    return VideoSearchEngine(
        clip_model_name=clip_model_name,
        output_dir=output_dir,
        frame_interval=frame_interval
    )
