"""
CLIP model integration for text-to-image matching in video search.
"""

import torch
import numpy as np
from PIL import Image
from transformers import CLIPProcessor, CLIPModel
from typing import List, Tuple, Optional
import warnings
from tqdm import tqdm
import sys
import os

# Import advanced matching
try:
    from ..utils.advanced_matching import AdvancedMatcher
except ImportError:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.advanced_matching import AdvancedMatcher


class CLIPMatcher:
    """CLIP-based text-to-image matching for video frame search."""
    
    def __init__(self, model_name: str = "openai/clip-vit-base-patch32", device: Optional[str] = None):
        """
        Initialize CLIP model for text-image matching.
        
        Args:
            model_name: HuggingFace model name for CLIP
            device: Device to run model on ('cuda', 'cpu', or None for auto-detect)
        """
        self.model_name = model_name
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        
        print(f"Loading CLIP model: {model_name}")
        print(f"Using device: {self.device}")
        
        # Load model and processor
        self.model = CLIPModel.from_pretrained(model_name)
        self.processor = CLIPProcessor.from_pretrained(model_name)
        
        # Move model to device
        self.model = self.model.to(self.device)
        self.model.eval()

        # Initialize advanced matcher
        self.advanced_matcher = AdvancedMatcher(self.model, self.processor, self.device)

        print("CLIP model loaded successfully")
    
    def encode_text(self, text: str) -> torch.Tensor:
        """
        Encode text query using CLIP text encoder.
        
        Args:
            text: Text query to encode
            
        Returns:
            Text embedding tensor
        """
        with torch.no_grad():
            inputs = self.processor(text=[text], return_tensors="pt", padding=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            text_features = self.model.get_text_features(**inputs)
            # Normalize features
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            return text_features
    
    def encode_images(self, images: List[np.ndarray], batch_size: int = 4) -> torch.Tensor:
        """
        Encode images using CLIP image encoder.
        
        Args:
            images: List of image arrays (RGB format)
            batch_size: Batch size for processing images
            
        Returns:
            Image embeddings tensor
        """
        all_features = []
        
        # Process images in batches with memory management
        for i in tqdm(range(0, len(images), batch_size), desc="Encoding images"):
            batch_images = images[i:i + batch_size]

            try:
                # Convert numpy arrays to PIL Images
                pil_images = [Image.fromarray(img) for img in batch_images]

                with torch.no_grad():
                    inputs = self.processor(images=pil_images, return_tensors="pt", padding=True)
                    inputs = {k: v.to(self.device) for k, v in inputs.items()}

                    image_features = self.model.get_image_features(**inputs)
                    # Normalize features
                    image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                    all_features.append(image_features.cpu())

                    # Clear GPU memory
                    if self.device == 'cuda':
                        torch.cuda.empty_cache()

            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    print(f"GPU memory error, reducing batch size and retrying...")
                    # Process images one by one if batch fails
                    for single_img in batch_images:
                        pil_img = Image.fromarray(single_img)
                        with torch.no_grad():
                            inputs = self.processor(images=[pil_img], return_tensors="pt", padding=True)
                            inputs = {k: v.to(self.device) for k, v in inputs.items()}

                            image_features = self.model.get_image_features(**inputs)
                            image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                            all_features.append(image_features.cpu())

                            if self.device == 'cuda':
                                torch.cuda.empty_cache()
                else:
                    raise e
        
        return torch.cat(all_features, dim=0)
    
    def compute_similarity(self, text_features: torch.Tensor, image_features: torch.Tensor) -> torch.Tensor:
        """
        Compute cosine similarity between text and image features.
        
        Args:
            text_features: Text embedding tensor
            image_features: Image embeddings tensor
            
        Returns:
            Similarity scores tensor
        """
        # Ensure tensors are on the same device
        text_features = text_features.to(self.device)
        image_features = image_features.to(self.device)
        
        # Compute cosine similarity
        similarity = torch.matmul(text_features, image_features.T)
        return similarity.squeeze()
    
    def search_frames(self,
                     frames: List[Tuple[int, np.ndarray, float]],
                     query: str,
                     threshold: float = 0.2,
                     top_k: Optional[int] = None,
                     batch_size: int = 8,
                     use_advanced_matching: bool = True) -> List[Tuple[int, np.ndarray, float, float]]:
        """
        Search for frames matching the text query.

        Args:
            frames: List of (frame_index, frame_array, timestamp) tuples
            query: Text query to search for
            threshold: Minimum similarity threshold
            top_k: Return top K results (None for all above threshold)
            batch_size: Batch size for image encoding
            use_advanced_matching: Use advanced matching techniques for better accuracy

        Returns:
            List of (frame_index, frame_array, timestamp, similarity_score) tuples
        """
        if not frames:
            return []
        
        print(f"Searching {len(frames)} frames for: '{query}'")
        
        # Extract images from frames
        images = [frame[1] for frame in frames]

        if use_advanced_matching:
            print(f"Using advanced matching for improved accuracy...")

            # Use multi-query matching
            similarities = self.advanced_matcher.multi_query_matching(images, query, batch_size)

            # Apply negative filtering to reduce false positives
            similarities = self.advanced_matcher.negative_filtering(images, query, similarities, batch_size)

            # Apply contextual scoring
            similarities = self.advanced_matcher.contextual_scoring(images, query, similarities, batch_size)

            similarities = np.array(similarities)
        else:
            # Standard CLIP matching
            text_features = self.encode_text(query)
            image_features = self.encode_images(images, batch_size=batch_size)
            similarities = self.compute_similarity(text_features, image_features)
            similarities = similarities.cpu().numpy()
        
        # Create results with similarity scores
        results = []
        for i, (frame_idx, frame_array, timestamp) in enumerate(frames):
            score = float(similarities[i])
            if score >= threshold:
                results.append((frame_idx, frame_array, timestamp, score))
        
        # Sort by similarity score (descending)
        results.sort(key=lambda x: x[3], reverse=True)
        
        # Apply top_k limit if specified
        if top_k is not None:
            results = results[:top_k]
        
        print(f"Found {len(results)} matching frames (threshold: {threshold})")
        return results
    
    def batch_search(self, 
                    frames: List[Tuple[int, np.ndarray, float]], 
                    queries: List[str],
                    threshold: float = 0.2,
                    batch_size: int = 8) -> dict:
        """
        Search for multiple queries in the same set of frames.
        
        Args:
            frames: List of (frame_index, frame_array, timestamp) tuples
            queries: List of text queries to search for
            threshold: Minimum similarity threshold
            batch_size: Batch size for image encoding
            
        Returns:
            Dictionary mapping queries to their results
        """
        if not frames or not queries:
            return {}
        
        print(f"Batch searching {len(frames)} frames for {len(queries)} queries")
        
        # Extract images from frames
        images = [frame[1] for frame in frames]
        
        # Encode images once
        image_features = self.encode_images(images, batch_size=batch_size)
        
        results = {}
        
        # Search for each query
        for query in queries:
            print(f"Searching for: '{query}'")
            
            # Encode text query
            text_features = self.encode_text(query)
            
            # Compute similarities
            similarities = self.compute_similarity(text_features, image_features)
            similarities = similarities.cpu().numpy()
            
            # Create results for this query
            query_results = []
            for i, (frame_idx, frame_array, timestamp) in enumerate(frames):
                score = float(similarities[i])
                if score >= threshold:
                    query_results.append((frame_idx, frame_array, timestamp, score))
            
            # Sort by similarity score (descending)
            query_results.sort(key=lambda x: x[3], reverse=True)
            results[query] = query_results
            
            print(f"Found {len(query_results)} matches for '{query}'")
        
        return results


def create_clip_matcher(model_name: str = "openai/clip-vit-base-patch32") -> CLIPMatcher:
    """
    Factory function to create a CLIP matcher.
    
    Args:
        model_name: HuggingFace model name for CLIP
        
    Returns:
        CLIPMatcher instance
    """
    return CLIPMatcher(model_name=model_name)
