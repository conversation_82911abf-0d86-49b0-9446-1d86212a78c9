"""
AI-Powered Video Content Search Application

This is the main entry point for the video search application.
It provides both a command-line interface and a Streamlit web interface.

Usage:
    # Run Streamlit web interface
    python main.py --web
    
    # Run command-line interface
    python main.py --video path/to/video.mp4 --query "red cap"
    
    # Show help
    python main.py --help
"""

import argparse
import os
import sys
import subprocess
from typing import Optional, List, Tuple

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.clip_match import VideoSearchEngine
from utils.frame_extraction import FrameExtractor


def run_streamlit_app():
    """Launch the Streamlit web interface."""
    print("🚀 Starting AI Video Content Search Web Interface...")
    print("📱 The application will open in your default web browser")
    print("🔗 URL: http://localhost:8501")
    print("\n💡 Tips:")
    print("   - Upload a video file (MP4, AVI, MOV, etc.)")
    print("   - Enter search queries like 'red cap', 'person walking', 'car driving'")
    print("   - Adjust similarity threshold for more/fewer results")
    print("   - Enable video clips and thumbnails for better visualization")
    print("\n⏹️  Press Ctrl+C to stop the application")
    print("-" * 60)
    
    # Run Streamlit
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            "app/interface.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running Streamlit: {e}")
        print("💡 Make sure Streamlit is installed: pip install streamlit")


def run_cli_search(video_path: str,
                  query: str,
                  similarity_threshold: float = 0.2,
                  max_results: int = 10,
                  create_clips: bool = False,
                  create_thumbnails: bool = False,
                  output_dir: str = "static/output_clips",
                  frame_interval: int = 30,
                  target_resolution: Optional[Tuple[int, int]] = (512, 384),
                  quality_factor: float = 0.8):
    """Run video search from command line."""
    
    print(f"🎬 AI Video Content Search")
    print(f"📹 Video: {video_path}")
    print(f"🔍 Query: '{query}'")
    print(f"🎯 Threshold: {similarity_threshold}")
    print("-" * 60)
    
    # Validate video file
    if not os.path.exists(video_path):
        print(f"❌ Error: Video file not found: {video_path}")
        return False
    
    try:
        # Create search engine
        print("🤖 Initializing AI models...")
        search_engine = VideoSearchEngine(
            output_dir=output_dir,
            frame_interval=frame_interval,
            target_resolution=target_resolution,
            quality_factor=quality_factor,
            use_chunked_processing=True
        )
        
        # Run search
        print("🔍 Searching video...")
        results = search_engine.search_video(
            video_path=video_path,
            query=query,
            similarity_threshold=similarity_threshold,
            top_k=max_results,
            create_clips=create_clips,
            create_thumbnails=create_thumbnails
        )
        
        # Display results
        print("\n📊 SEARCH RESULTS")
        print("=" * 60)
        
        if results['matches']:
            print(f"✅ Found {len(results['matches'])} matches!")
            print(f"⏱️  Processing time: {results['processing_time']:.2f} seconds")
            print(f"🎞️  Analyzed {results['stats']['total_frames_extracted']} frames")
            
            print("\n📋 MATCHES:")
            for i, match in enumerate(results['matches'][:10]):  # Show top 10
                print(f"  {i+1:2d}. Time: {match['time_formatted']} | "
                      f"Score: {match['similarity_score']:.3f} | "
                      f"Frame: {match['frame_index']}")
            
            if len(results['matches']) > 10:
                print(f"     ... and {len(results['matches']) - 10} more matches")
            
            if results['clips']:
                print(f"\n🎬 Created {len(results['clips'])} video clips:")
                for clip in results['clips']:
                    print(f"   📁 {clip}")
            
            if results['thumbnails']:
                print(f"\n🖼️  Created {len(results['thumbnails'])} thumbnails:")
                for thumb in results['thumbnails'][:5]:  # Show first 5
                    print(f"   📁 {thumb['path']} (score: {thumb['score']:.3f})")
        
        else:
            print("❌ No matches found.")
            print("💡 Try:")
            print("   - Lowering the similarity threshold")
            print("   - Using different search terms")
            print("   - Checking if the content actually exists in the video")
        
        print("\n" + "=" * 60)
        return True
        
    except Exception as e:
        print(f"❌ Error during search: {e}")
        return False


def show_video_info(video_path: str):
    """Show information about a video file."""
    if not os.path.exists(video_path):
        print(f"❌ Error: Video file not found: {video_path}")
        return
    
    try:
        extractor = FrameExtractor()
        info = extractor.get_video_info(video_path)
        
        print(f"📹 VIDEO INFORMATION")
        print("=" * 40)
        print(f"📁 File: {video_path}")
        print(f"⏱️  Duration: {info['duration_seconds']:.1f} seconds")
        print(f"🎞️  FPS: {info['fps']:.1f}")
        print(f"📐 Resolution: {info['width']}x{info['height']}")
        print(f"🎬 Total Frames: {info['total_frames']:,}")
        print("=" * 40)
        
    except Exception as e:
        print(f"❌ Error reading video: {e}")


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="AI-Powered Video Content Search",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Launch web interface
  python main.py --web
  
  # Search from command line
  python main.py --video video.mp4 --query "red cap"
  
  # Search with custom settings
  python main.py --video video.mp4 --query "person walking" --threshold 0.15 --clips
  
  # Show video information
  python main.py --info video.mp4
        """
    )
    
    # Main mode selection
    mode_group = parser.add_mutually_exclusive_group(required=True)
    mode_group.add_argument("--web", action="store_true", 
                           help="Launch Streamlit web interface")
    mode_group.add_argument("--video", type=str, 
                           help="Path to video file for CLI search")
    mode_group.add_argument("--info", type=str, 
                           help="Show information about a video file")
    
    # CLI search options
    parser.add_argument("--query", type=str, 
                       help="Search query (required with --video)")
    parser.add_argument("--threshold", type=float, default=0.2,
                       help="Similarity threshold (0.1-0.5, default: 0.2)")
    parser.add_argument("--max-results", type=int, default=10,
                       help="Maximum number of results (default: 10)")
    parser.add_argument("--clips", action="store_true",
                       help="Create video clips from matches")
    parser.add_argument("--thumbnails", action="store_true",
                       help="Create thumbnail images from matches")
    parser.add_argument("--output-dir", type=str, default="static/output_clips",
                       help="Output directory for clips and thumbnails")

    # Large video processing options
    parser.add_argument("--frame-interval", type=int, default=30,
                       help="Extract every N frames (default: 30, higher = faster)")
    parser.add_argument("--resolution", type=str, choices=["high", "medium", "low", "original"],
                       default="medium", help="Processing resolution (default: medium)")
    parser.add_argument("--quality", type=float, default=0.8,
                       help="Compression quality 0.1-1.0 (default: 0.8)")
    
    args = parser.parse_args()
    
    # Handle different modes
    if args.web:
        run_streamlit_app()
    
    elif args.info:
        show_video_info(args.info)
    
    elif args.video:
        if not args.query:
            print("❌ Error: --query is required when using --video")
            parser.print_help()
            sys.exit(1)

        # Parse resolution setting
        resolution_map = {
            "high": (720, 540),
            "medium": (512, 384),
            "low": (320, 240),
            "original": None
        }
        target_resolution = resolution_map[args.resolution]

        success = run_cli_search(
            video_path=args.video,
            query=args.query,
            similarity_threshold=args.threshold,
            max_results=args.max_results,
            create_clips=args.clips,
            create_thumbnails=args.thumbnails,
            output_dir=args.output_dir,
            frame_interval=args.frame_interval,
            target_resolution=target_resolution,
            quality_factor=args.quality
        )
        
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
