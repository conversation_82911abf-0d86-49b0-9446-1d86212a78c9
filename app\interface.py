"""
Streamlit user interface for AI-powered video content search.
"""

import streamlit as st
import os
import tempfile
import shutil
from typing import Dict, Any, List
import time

# Import our modules
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils.clip_match import VideoSearchEngine
except ImportError:
    # Alternative import path
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from utils.clip_match import VideoSearchEngine


def initialize_session_state():
    """Initialize Streamlit session state variables."""
    if 'search_engine' not in st.session_state:
        st.session_state.search_engine = None
    if 'current_video_path' not in st.session_state:
        st.session_state.current_video_path = None
    if 'search_results' not in st.session_state:
        st.session_state.search_results = None
    if 'video_uploaded' not in st.session_state:
        st.session_state.video_uploaded = False


def create_search_engine(frame_interval=30, target_resolution=(512, 384), quality_factor=0.8):
    """Create and cache the search engine."""
    if st.session_state.search_engine is None:
        with st.spinner("Initializing AI models... This may take a moment."):
            st.session_state.search_engine = VideoSearchEngine(
                clip_model_name="openai/clip-vit-base-patch32",
                output_dir="static/output_clips",
                frame_interval=frame_interval,
                target_resolution=target_resolution,
                quality_factor=quality_factor,
                use_chunked_processing=True,
                chunk_size=200
            )
    return st.session_state.search_engine


def save_uploaded_video(uploaded_file) -> str:
    """Save uploaded video to temporary location."""
    # Create temp directory if it doesn't exist
    temp_dir = "temp_videos"
    os.makedirs(temp_dir, exist_ok=True)
    
    # Save uploaded file
    temp_path = os.path.join(temp_dir, uploaded_file.name)
    with open(temp_path, "wb") as f:
        f.write(uploaded_file.getbuffer())
    
    return temp_path


def display_video_info(video_info: Dict[str, Any]):
    """Display video information in a nice format."""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Duration", f"{video_info['duration_seconds']:.1f}s")
    with col2:
        st.metric("FPS", f"{video_info['fps']:.1f}")
    with col3:
        st.metric("Resolution", f"{video_info['width']}x{video_info['height']}")
    with col4:
        st.metric("Total Frames", f"{video_info['total_frames']:,}")


def display_search_results(results: Dict[str, Any]):
    """Display search results with clips and thumbnails."""
    if not results['matches']:
        st.warning("No matches found. Try adjusting the similarity threshold or using different search terms.")
        return
    
    st.success(f"Found {len(results['matches'])} matches!")
    
    # Display processing stats
    with st.expander("Search Statistics"):
        stats = results['stats']
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Processing Time", f"{results['processing_time']:.2f}s")
        with col2:
            st.metric("Frames Analyzed", f"{stats['total_frames_extracted']:,}")
        with col3:
            st.metric("Similarity Threshold", f"{stats['similarity_threshold']:.2f}")
    
    # Display results
    st.subheader("Search Results")
    
    # Create tabs for different result types
    tab1, tab2, tab3 = st.tabs(["📋 Match List", "🎬 Video Clips", "🖼️ Thumbnails"])
    
    with tab1:
        # Display match list
        for i, match in enumerate(results['matches']):
            with st.container():
                col1, col2, col3 = st.columns([2, 1, 1])
                with col1:
                    st.write(f"**Match {i+1}**")
                    st.write(f"Time: {match['time_formatted']} ({match['timestamp']:.2f}s)")
                with col2:
                    st.metric("Similarity", f"{match['similarity_score']:.3f}")
                with col3:
                    st.write(f"Frame: {match['frame_index']}")
                st.divider()
    
    with tab2:
        # Display video clips
        if results['clips']:
            st.write(f"Generated {len(results['clips'])} video clips:")
            
            # Display clips in a grid
            cols = st.columns(2)
            for i, clip_path in enumerate(results['clips']):
                with cols[i % 2]:
                    if os.path.exists(clip_path):
                        st.video(clip_path)
                        st.caption(f"Clip {i+1}: {os.path.basename(clip_path)}")
                    else:
                        st.error(f"Clip file not found: {clip_path}")
        else:
            st.info("No video clips were generated. Enable clip creation in the settings.")
    
    with tab3:
        # Display thumbnails
        if results['thumbnails']:
            st.write(f"Generated {len(results['thumbnails'])} thumbnails:")
            
            # Display thumbnails in a grid
            cols = st.columns(3)
            for i, thumbnail in enumerate(results['thumbnails']):
                with cols[i % 3]:
                    if os.path.exists(thumbnail['path']):
                        st.image(thumbnail['path'], caption=f"Score: {thumbnail['score']:.3f}")
                    else:
                        st.error(f"Thumbnail not found: {thumbnail['path']}")
        else:
            st.info("No thumbnails were generated. Enable thumbnail creation in the settings.")


def main():
    """Main Streamlit application."""
    st.set_page_config(
        page_title="AI Video Content Search",
        page_icon="🎬",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Initialize session state
    initialize_session_state()
    
    # Header
    st.title("🎬 AI-Powered Video Content Search")
    st.markdown("Upload a video and search for specific objects, people, or scenes using natural language!")
    
    # Sidebar for settings
    with st.sidebar:
        st.header("⚙️ Settings")
        
        # Search settings
        similarity_threshold = st.slider(
            "Similarity Threshold",
            min_value=0.1,
            max_value=0.5,
            value=0.2,
            step=0.05,
            help="Lower values return more results but may be less accurate"
        )

        max_results = st.number_input(
            "Max Results",
            min_value=1,
            max_value=100,
            value=20,
            help="Maximum number of results to return"
        )

        # Large video settings
        st.subheader("Large Video Settings")
        frame_interval = st.slider(
            "Frame Interval",
            min_value=15,
            max_value=120,
            value=30,
            step=15,
            help="Extract every N frames. Higher values = faster processing, lower accuracy"
        )

        target_resolution = st.selectbox(
            "Processing Resolution",
            options=[
                ("High Quality (720x540)", (720, 540)),
                ("Medium Quality (512x384)", (512, 384)),
                ("Low Quality (320x240)", (320, 240)),
                ("Original Resolution", None)
            ],
            index=1,
            help="Lower resolution = faster processing and less memory usage"
        )

        quality_factor = st.slider(
            "Compression Quality",
            min_value=0.3,
            max_value=1.0,
            value=0.8,
            step=0.1,
            help="Lower values = more compression, less memory usage"
        )
        
        # Output settings
        st.subheader("Output Options")
        create_clips = st.checkbox("Create Video Clips", value=True)
        create_thumbnails = st.checkbox("Create Thumbnails", value=True)
        
        if create_clips:
            clip_duration = st.slider(
                "Clip Duration (seconds)",
                min_value=1.0,
                max_value=10.0,
                value=3.0,
                step=0.5
            )
        else:
            clip_duration = 3.0
        
        # Cache management
        st.subheader("Memory & Cache")

        # Show cache info
        if st.session_state.search_engine:
            cache_info = st.session_state.search_engine.get_cache_info()
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Cached Videos", len(cache_info['cached_videos']))
                st.metric("Memory Usage", f"{cache_info['memory_usage_mb']:.1f} MB")
            with col2:
                st.metric("Cached Frames", cache_info['total_cached_frames'])
                st.metric("Memory Limit", f"{cache_info['memory_limit_mb']} MB")

        if st.button("Clear Cache"):
            if st.session_state.search_engine:
                st.session_state.search_engine.clear_cache()
                st.success("Cache cleared!")

        if st.button("Clean Output Directory"):
            if st.session_state.search_engine:
                st.session_state.search_engine.cleanup_outputs()
                st.success("Output directory cleaned!")
    
    # Main content area
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.header("📁 Upload Video")
        
        # Video upload
        uploaded_file = st.file_uploader(
            "Choose a video file",
            type=['mp4', 'avi', 'mov', 'mkv', 'wmv'],
            help="Supported formats: MP4, AVI, MOV, MKV, WMV"
        )
        
        if uploaded_file is not None:
            # Save uploaded video
            if not st.session_state.video_uploaded or st.session_state.current_video_path != uploaded_file.name:
                with st.spinner("Uploading video..."):
                    video_path = save_uploaded_video(uploaded_file)
                    st.session_state.current_video_path = video_path
                    st.session_state.video_uploaded = True
                    st.session_state.search_results = None  # Clear previous results
                
                st.success("Video uploaded successfully!")
            
            # Display video
            st.video(st.session_state.current_video_path)
            
            # Create search engine with current settings
            search_engine = create_search_engine(
                frame_interval=frame_interval,
                target_resolution=target_resolution[1],
                quality_factor=quality_factor
            )
            
            # Get and display video info
            try:
                video_info = search_engine._get_video_info(st.session_state.current_video_path)
                st.subheader("📊 Video Information")
                display_video_info(video_info)
            except Exception as e:
                st.error(f"Error reading video: {e}")
    
    with col2:
        st.header("🔍 Search")
        
        if st.session_state.video_uploaded:
            # Search interface
            query = st.text_input(
                "What are you looking for?",
                placeholder="e.g., 'red cap', 'person walking', 'car driving', 'sunset'",
                help="Describe what you want to find in the video using natural language"
            )
            
            # Search button
            if st.button("🔍 Search Video", type="primary", disabled=not query):
                if query:
                    search_engine = create_search_engine(
                        frame_interval=frame_interval,
                        target_resolution=target_resolution[1],
                        quality_factor=quality_factor
                    )

                    with st.spinner(f"Searching for '{query}' in the video..."):
                        try:
                            results = search_engine.search_video(
                                video_path=st.session_state.current_video_path,
                                query=query,
                                similarity_threshold=similarity_threshold,
                                top_k=max_results,
                                create_clips=create_clips,
                                clip_duration=clip_duration,
                                create_thumbnails=create_thumbnails
                            )
                            st.session_state.search_results = results
                        except Exception as e:
                            st.error(f"Search failed: {e}")
                            st.error("💡 Try reducing frame interval or processing resolution for large videos")
                            st.session_state.search_results = None
            
            # Display search results
            if st.session_state.search_results:
                st.divider()
                display_search_results(st.session_state.search_results)
            
            # Example queries
            st.subheader("💡 Example Queries")
            example_queries = [
                "person wearing red",
                "car on the road",
                "dog running",
                "sunset or sunrise",
                "people talking",
                "green trees",
                "building or house",
                "water or ocean"
            ]
            
            cols = st.columns(2)
            for i, example in enumerate(example_queries):
                with cols[i % 2]:
                    if st.button(f"'{example}'", key=f"example_{i}"):
                        st.rerun()
        
        else:
            st.info("👆 Please upload a video file to start searching!")


if __name__ == "__main__":
    main()
