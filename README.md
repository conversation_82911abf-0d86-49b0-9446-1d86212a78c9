# 🎬 AI-Powered Video Content Search

An intelligent video search application that allows users to find specific objects, people, or scenes in videos using natural language queries. Built with CLIP (Contrastive Language-Image Pre-training) for state-of-the-art text-to-image matching.

## ✨ Features

- **🔍 Natural Language Search**: Search videos using descriptive text like "red cap", "person walking", or "sunset"
- **🎬 Video Clip Generation**: Automatically create video clips from matching scenes
- **🖼️ Thumbnail Extraction**: Generate thumbnail images of matching frames
- **📊 Interactive Web Interface**: User-friendly Streamlit interface with drag-and-drop video upload
- **⚡ Batch Processing**: Search for multiple queries in the same video efficiently
- **🎯 Adjustable Precision**: Customize similarity thresholds for more or fewer results
- **💾 Smart Caching**: Cache extracted frames for faster repeated searches

## 🚀 Quick Start

### Installation

1. **Clone or download the project**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

### Web Interface (Recommended)

Launch the interactive web application:

```bash
python main.py --web
```

Then open your browser to `http://localhost:8501`

### Command Line Interface

Search a video from the command line:

```bash
# Basic search
python main.py --video path/to/video.mp4 --query "red cap"

# Advanced search with clips and thumbnails
python main.py --video video.mp4 --query "person walking" --threshold 0.15 --clips --thumbnails

# Show video information
python main.py --info video.mp4
```

## 📁 Project Structure

```
video_search_ai/
├── main.py                 # Main application entry point
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── utils/                 # Core utilities
│   ├── __init__.py
│   ├── frame_extraction.py    # Video frame extraction
│   ├── clip_match.py         # Main search engine
│   └── video_slicing.py      # Video clip generation
├── models/                # AI models
│   ├── __init__.py
│   └── clip_model.py         # CLIP model integration
├── app/                   # Web interface
│   ├── __init__.py
│   └── interface.py          # Streamlit UI
└── static/                # Output directory
    └── output_clips/         # Generated clips and thumbnails
```

## 🛠️ Technology Stack

- **🐍 Python**: Core programming language
- **🤖 CLIP**: OpenAI's text-image matching model
- **🎥 OpenCV**: Video processing and frame extraction
- **🎬 MoviePy**: Video clip generation
- **🌐 Streamlit**: Interactive web interface
- **🔥 PyTorch**: Deep learning framework
- **🤗 Transformers**: Hugging Face model library

## 📖 How It Works

1. **Frame Extraction**: Extract frames from video at regular intervals
2. **AI Analysis**: Use CLIP to encode both the search query and video frames
3. **Similarity Matching**: Compute cosine similarity between text and image embeddings
4. **Result Processing**: Filter and rank results by similarity score
5. **Output Generation**: Create video clips and thumbnails from matching frames

## 🎯 Usage Examples

### Web Interface

1. **Upload Video**: Drag and drop or select a video file
2. **Enter Query**: Type what you're looking for (e.g., "dog running")
3. **Adjust Settings**: Set similarity threshold and output options
4. **Search**: Click search and view results with clips and thumbnails

### Command Line Examples

```bash
# Find people in red clothing
python main.py --video party.mp4 --query "person wearing red" --clips

# Search for vehicles
python main.py --video traffic.mp4 --query "car or truck" --threshold 0.25

# Find outdoor scenes
python main.py --video vacation.mp4 --query "trees and sky" --thumbnails

# Batch search (modify the code to support multiple queries)
python main.py --video movie.mp4 --query "action scene" --max-results 20
```

## ⚙️ Configuration Options

### Search Parameters

- **Similarity Threshold** (0.1-0.5): Lower values return more results
- **Max Results**: Maximum number of matches to return
- **Frame Interval**: Extract every N frames (default: 30)

### Output Options

- **Create Clips**: Generate video clips from matches
- **Clip Duration**: Length of each clip in seconds
- **Create Thumbnails**: Generate thumbnail images
- **Output Directory**: Where to save clips and thumbnails

## 🎨 Example Queries

Try these search queries to get started:

- **Objects**: "red cap", "blue car", "white dog", "laptop computer"
- **People**: "person walking", "child playing", "woman talking"
- **Scenes**: "sunset", "beach", "forest", "city street"
- **Actions**: "running", "dancing", "cooking", "driving"
- **Colors**: "something red", "green trees", "blue sky"

## 🔧 Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce batch size or use CPU
2. **Video Format Not Supported**: Convert to MP4 using FFmpeg
3. **Slow Processing**: Increase frame interval or reduce video resolution
4. **No Matches Found**: Lower similarity threshold or try different queries

### Performance Tips

- Use shorter videos for faster processing
- Increase frame interval (e.g., 60) for longer videos
- Enable GPU acceleration if available
- Clear cache between different videos

## 📊 Performance Metrics

Typical processing times (on modern hardware):

- **Frame Extraction**: ~1-2 seconds per minute of video
- **CLIP Encoding**: ~0.1 seconds per frame
- **Clip Generation**: ~2-5 seconds per clip
- **Total Search Time**: ~30-60 seconds for a 5-minute video

## 🤝 Contributing

Contributions are welcome! Areas for improvement:

- Support for more video formats
- Additional AI models (YOLOv8, DETR)
- Real-time video search
- Advanced filtering options
- Mobile app interface

## 📄 License

This project is open source. Feel free to use, modify, and distribute.

## 🙏 Acknowledgments

- **OpenAI CLIP**: For the amazing text-image matching capabilities
- **Hugging Face**: For easy model access and transformers library
- **Streamlit**: For the beautiful and simple web interface
- **MoviePy & OpenCV**: For robust video processing tools

---

**Happy Searching! 🎬✨**
